{"name": "web-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3004", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@tap2go/database": "workspace:*", "@tap2go/firebase-config": "workspace:*", "@tap2go/shared-auth": "workspace:*", "@tap2go/shared-types": "workspace:*", "@tap2go/shared-ui": "workspace:*", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}